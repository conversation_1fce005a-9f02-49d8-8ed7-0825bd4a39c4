{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrahead %}{{ block.super }}
<script src="{% url 'admin:jsi18n' %}"></script>
{{ media }}
<style>
    .bulk-form-container {
        max-width: 800px;
        margin: 20px auto;
        padding: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .form-row {
        margin-bottom: 20px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: #f9f9f9;
    }
    
    .form-row label {
        font-weight: bold;
        display: block;
        margin-bottom: 8px;
        color: #333;
    }
    
    .form-row .helptext {
        font-size: 11px;
        color: #666;
        margin-top: 5px;
    }
    
    .attribute-checkbox-list {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #ccc;
        padding: 10px;
        background: white;
        border-radius: 4px;
    }
    
    .attribute-checkbox-list li {
        list-style: none;
        margin: 8px 0;
        padding: 5px;
        border-bottom: 1px solid #eee;
    }
    
    .attribute-checkbox-list li:last-child {
        border-bottom: none;
    }
    
    .attribute-checkbox-list input[type="checkbox"] {
        margin-right: 8px;
    }
    
    .global-settings {
        background-color: #e8f4f8;
        border-left: 4px solid #17a2b8;
    }
    
    .global-settings h3 {
        margin-top: 0;
        color: #17a2b8;
        font-size: 16px;
    }
    
    .checkbox-row {
        display: flex;
        gap: 30px;
        margin-top: 10px;
    }
    
    .checkbox-item {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .submit-row {
        text-align: right;
        padding: 20px 0;
        border-top: 1px solid #ddd;
        margin-top: 20px;
    }
    
    .btn-primary {
        background-color: #417690;
        border-color: #417690;
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        text-decoration: none;
        display: inline-block;
        border: none;
        cursor: pointer;
        font-size: 14px;
    }
    
    .btn-primary:hover {
        background-color: #205067;
        border-color: #205067;
    }
    
    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        text-decoration: none;
        display: inline-block;
        margin-right: 10px;
    }
    
    .errorlist {
        color: #dc3545;
        list-style: none;
        padding: 0;
        margin: 10px 0;
    }
    
    .errorlist li {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 4px;
        padding: 8px 12px;
        margin-bottom: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="bulk-form-container">
    <h1>{{ title }}</h1>
    
    <p>Use this form to associate multiple attributes with a single product type in one operation. 
    This is more efficient than adding them one by one.</p>
    
    <form method="post">
        {% csrf_token %}
        
        {% if form.non_field_errors %}
            <ul class="errorlist">
                {% for error in form.non_field_errors %}
                    <li>{{ error }}</li>
                {% endfor %}
            </ul>
        {% endif %}
        
        <div class="form-row">
            <label for="{{ form.product_type.id_for_label }}">{{ form.product_type.label }}:</label>
            {{ form.product_type }}
            {% if form.product_type.errors %}
                <ul class="errorlist">
                    {% for error in form.product_type.errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
            {% if form.product_type.help_text %}
                <div class="helptext">{{ form.product_type.help_text }}</div>
            {% endif %}
        </div>
        
        <div class="form-row">
            <label for="{{ form.attributes.id_for_label }}">{{ form.attributes.label }}:</label>
            {{ form.attributes }}
            {% if form.attributes.errors %}
                <ul class="errorlist">
                    {% for error in form.attributes.errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
            {% if form.attributes.help_text %}
                <div class="helptext">{{ form.attributes.help_text }}</div>
            {% endif %}
        </div>
        
        <div class="form-row global-settings">
            <h3>Global Settings</h3>
            <p>These settings will be applied to all selected attributes:</p>
            
            <div class="checkbox-row">
                <div class="checkbox-item">
                    {{ form.is_filterable }}
                    <label for="{{ form.is_filterable.id_for_label }}">{{ form.is_filterable.label }}</label>
                </div>
                
                <div class="checkbox-item">
                    {{ form.is_option_selector }}
                    <label for="{{ form.is_option_selector.id_for_label }}">{{ form.is_option_selector.label }}</label>
                </div>
            </div>
            
            {% if form.is_filterable.help_text %}
                <div class="helptext">{{ form.is_filterable.help_text }}</div>
            {% endif %}
            {% if form.is_option_selector.help_text %}
                <div class="helptext">{{ form.is_option_selector.help_text }}</div>
            {% endif %}
        </div>
        
        <div class="submit-row">
            <a href="{% url 'admin:products_producttypeattributeproxy_changelist' %}" class="btn-secondary">Cancel</a>
            <input type="submit" value="Save and add another" name="_addanother" class="btn-primary">
            <input type="submit" value="Save" class="btn-primary">
        </div>
    </form>
</div>
{% endblock %}
