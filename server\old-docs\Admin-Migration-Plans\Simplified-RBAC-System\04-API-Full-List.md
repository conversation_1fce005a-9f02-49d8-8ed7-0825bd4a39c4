# API Full List - Staff RBAC System

## **Staff Authorization Endpoints**

### **1. Get Staff User Info**
**Available requests:** [GET]

**GET:** `/api/staff/auth/user/` [Staff Users]

Retrieves current authenticated staff user information including groups and permissions.

```json
No request body required
```

---

### **2. Check User Permissions**
**Available requests:** [GET]

**GET:** `/api/staff/auth/permissions/` [Staff Users]

Gets detailed permission information for the current user including group permissions breakdown.

```json
No request body required
```

---

### **3. Check Specific Permission**
**Available requests:** [POST]

**POST:** `/api/staff/auth/check-permission/` [Staff Users]

Validates if the current user has a specific permission.

```json
{
    "permission": "products.add_product"
}
```

---

## **Group Management Endpoints**

### **4. List All Groups**
**Available requests:** [GET, POST]

**GET:** `/api/staff/groups/` [Staff Users]

Retrieves all available groups with optional search and filtering parameters.

```json
No request body required
```

**POST:** `/api/staff/groups/` [Staff Users with Group Management Permission]

Creates a new staff group with specified permissions.

```json
{
    "name": "New Staff Group",
    "permission_ids": [25, 26, 27, 28]
}
```

---

### **5. Group Detail Operations**
**Available requests:** [GET, PUT, PATCH, DELETE]

**GET:** `/api/staff/groups/{group_id}/` [Staff Users]

Retrieves detailed information about a specific group including members and permissions.

```json
No request body required
```

**PUT:** `/api/staff/groups/{group_id}/` [Staff Users with Group Management Permission]

Updates all fields of a specific group including name and permissions.

```json
{
    "name": "Updated Group Name",
    "permission_ids": [25, 26, 27]
}
```

**PATCH:** `/api/staff/groups/{group_id}/` [Staff Users with Group Management Permission]

Partially updates specific fields of a group.

```json
{
    "name": "Partially Updated Group Name"
}
```

**DELETE:** `/api/staff/groups/{group_id}/` [Superusers Only]

Permanently deletes a group and removes all user memberships.

```json
No request body required
```

---

### **6. Get Group Members**
**Available requests:** [GET]

**GET:** `/api/staff/groups/{group_id}/members/` [Staff Users]

Retrieves all members of a specific group with membership details.

```json
No request body required
```

---

### **7. Add User to Group**
**Available requests:** [POST]

**POST:** `/api/staff/groups/{group_id}/add_member/` [Staff Users with Group Management Permission]

Adds any active user (staff or regular) to a specific group with optional notes.

```json
{
    "user_id": 5,
    "notes": "Adding user to product management team"
}
```

---

### **8. Remove User from Group**
**Available requests:** [POST]

**POST:** `/api/staff/groups/{group_id}/remove_member/` [Staff Users with Group Management Permission]

Removes a user from a specific group.

```json
{
    "user_id": 5
}
```

---

### **9. Bulk User Assignment to Group**
**Available requests:** [POST]

**POST:** `/api/staff/groups/{group_id}/bulk_assign/` [Staff Users with Group Management Permission]

Assigns multiple active users (staff or regular) to a group in a single operation.

```json
{
    "user_ids": [5, 6, 7],
    "notes": "Bulk assignment to product management team"
}
```

---

## **User Management Endpoints**

### **10. List Users**
**Available requests:** [GET]

**GET:** `/api/staff/users/` [Staff Users with User Management Permission]

Retrieves all active users (staff and regular) with optional filtering and search capabilities.

```json
No request body required
```

---

### **11. Get User Details**
**Available requests:** [GET]

**GET:** `/api/staff/users/{user_id}/` [Staff Users with User Management Permission]

Retrieves detailed information about a specific user including groups and permissions.

```json
No request body required
```

---

### **12. Get User Groups**
**Available requests:** [GET]

**GET:** `/api/staff/users/{user_id}/groups/` [Staff Users with User Management Permission]

Retrieves all group memberships for a specific user with assignment details.

```json
No request body required
```

---

### **13. Toggle User Staff Status**
**Available requests:** [POST]

**POST:** `/api/staff/users/{user_id}/toggle_staff/` [Superusers Only]

Toggles the staff status of a user (promotes/demotes staff access).

```json
No request body required
```

---

## **Permission Management Endpoints**

### **14. List All Permissions**
**Available requests:** [GET]

**GET:** `/api/staff/permissions/` [Staff Users]

Retrieves all available permissions organized by app and model for group assignment.

```json
No request body required
```

---

## **Audit & Monitoring Endpoints**

### **15. List Audit Logs**
**Available requests:** [GET]

**GET:** `/api/staff/audit/` [Staff Users with Audit Access]

Retrieves audit logs with optional filtering by action, user, and date range.

```json
No request body required
```

---

### **16. Get Audit Summary**
**Available requests:** [GET]

**GET:** `/api/staff/audit/summary/` [Staff Users with Audit Access]

Retrieves audit summary statistics including action breakdowns and top users.

```json
No request body required
```

---

## **Authentication Endpoints (Core App)**

### **17. Staff Login**
**Available requests:** [POST]

**POST:** `/api/users/login/` [All Users]

Authenticates staff users and returns JWT tokens for API access.

```json
{
    "email": "<EMAIL>",
    "password": "your_password"
}
```

---

### **18. Staff Logout**
**Available requests:** [POST]

**POST:** `/api/users/logout/` [Authenticated Users]

Logs out the current user and invalidates the refresh token.

```json
{
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

---

### **19. Refresh Token**
**Available requests:** [POST]

**POST:** `/api/auth/token/refresh/` [All Users]

Refreshes the access token using a valid refresh token.

```json
{
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

---
