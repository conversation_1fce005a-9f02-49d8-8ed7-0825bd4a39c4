from rest_framework.permissions import BasePermission


class IsStaffUser(BasePermission):
    """
    Permission class to check if the user is staff
    Used for general staff API access
    """

    def has_permission(self, request, view):
        return (
                request.user and
                request.user.is_authenticated and
                request.user.is_staff
        )


class IsSuperUser(BasePermission):
    """
    Permission class to check if the user is a superuser
    Used for system administration endpoints
    """

    def has_permission(self, request, view):
        return (
                request.user and
                request.user.is_authenticated and
                request.user.is_superuser
        )


class CanManageGroups(BasePermission):
    """
    Permission class to manage groups.
    Allows staff users to create, read, update, and delete groups
    """

    def has_permission(self, request, view):
        # Check if the user is authenticated and is staff
        if not request.user.is_authenticated or not request.user.is_staff:
            return False

        # Allow all CRUD operations for staff users
        return True

    def has_object_permission(self, request, view, obj):
        # Check if the user is authenticated and is staff
        if not request.user.is_authenticated or not request.user.is_staff:
            return False

        # Allow all CRUD operations on group objects for staff users
        return True


class CanManageUsers(BasePermission):
    """
    Permission class to check if the user can manage other users
    Staff users can view and manage user-group assignments
    Only superusers can toggle staff status
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # For staff status toggle, require superuser
        if hasattr(view, 'action') and view.action == 'toggle_staff':
            return request.user.is_superuser

        return True


class CanManageStaff(BasePermission):
    """
    Permission class for staff user management operations
    Requires Staff Manager role or higher
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # Superusers can always manage staff
        if request.user.is_superuser:
            return True

        # Check if user has Staff Manager role
        staff_manager_groups = ['Staff Manager (SM)', 'HR Administrator (HRA)']
        user_groups = request.user.groups.values_list('name', flat=True)

        return any(group in staff_manager_groups for group in user_groups)


class CanCreateStaff(BasePermission):
    """
    Permission class for creating new staff users
    Requires Staff Manager or HR Administrator role
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # Superusers can always create staff
        if request.user.is_superuser:
            return True

        # Check if user has appropriate role
        authorized_groups = ['Staff Manager (SM)', 'HR Administrator (HRA)']
        user_groups = request.user.groups.values_list('name', flat=True)

        return any(group in authorized_groups for group in user_groups)


class CanManageDepartment(BasePermission):
    """
    Permission class for department-level management
    Requires Department Head role or higher
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # Superusers and Staff Managers can manage all departments
        if request.user.is_superuser:
            return True

        # Check if user has department management role
        management_groups = [
            'Staff Manager (SM)',
            'Department Head (DH)',
            'HR Administrator (HRA)'
        ]
        user_groups = request.user.groups.values_list('name', flat=True)

        return any(group in management_groups for group in user_groups)


class CanViewAuditLogs(BasePermission):
    """
    Permission class to check if the user can view audit logs
    Only staff users with specific permissions can view audit logs
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # Superusers can always view audit logs
        if request.user.is_superuser:
            return True

        # Check if user has audit viewing permission
        # This can be customized based on your specific requirements
        return request.user.has_perm('staff.view_permissionaudit')


class CanAccessStaffAPI(BasePermission):
    """
    Base permission for all staff API endpoints
    Ensures user is authenticated, active, and has staff status
    """

    def has_permission(self, request, view):
        return (
                request.user and
                request.user.is_authenticated and
                request.user.is_active and
                request.user.is_staff
        )

    def has_object_permission(self, request, view, obj):
        """
        Object-level permission check
        Can be overridden by specific views for fine-grained control
        """
        return self.has_permission(request, view)
