# Orders domain URLs for staff operations
from django.urls import path, include
from rest_framework.routers import DefaultRouter

# TODO: Import views when implemented
# from .views import OrderViewSet, OrderStatusViewSet

router = DefaultRouter()
# TODO: Register viewsets when implemented
# router.register(r'orders', OrderViewSet, basename='staff-orders')
# router.register(r'status', OrderStatusViewSet, basename='staff-order-status')

app_name = 'orders'

urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
]
